export const LINK = {
  LOGIN: "/auth",
  LOGOUT: "/logout",
  ACCOUNT_OVERVIEW: "/crafted/account/overview",
  ACCOUNT_SETTINGS: "/crafted/account/settings",

  WELCOME: "/welcome",
  HOMEPAGE: "/homepage",
  SETTING: "/setting",
  MY_FAVORITE: "/my-favorite",
  LATEST_PROJECTS: "/latest-projects",

  USERS: "/users",
  ORGANIZATIONS: "/organizations",

  CELL_TOWER_CREATE: "/cell-tower/create",
  CELL_TOWER_DETAIL: "/cell-tower/{0}",

  POWER_TOWER_CREATE: "/power-tower/create",
  POWER_TOWER_DETAIL: "/power-tower/{0}",

  POWER_LINE_CREATE: "/power-line/create",
  POWER_LINE_DETAIL: "/power-line/{0}",

  BUILDING_CREATE: "/building/create",
  BUILDING_DETAIL: "/building/{0}",

  ROAD_CREATE: "/road/create",
  ROAD_DETAIL: "/road/{0}",

  OBJECT_CREATE: "/objects/create",
  OBJECT_DETAIL: "/objects/{0}",

  CATEGORIES: "/categories",
  CATEGORY_CREATE: "/categories/create",
  CATEGORY_DETAIL: "/categories/{0}",

  ORG: "/org",
  ORG_CREATE: "/org/create",
  ORG_DETAIL: "/org/{0}",


  AI_ASSISTANCE_CREATE: "/ai-assistance/create",
  AI_ASSISTANCE_DETAIL: "/ai-assistance/{0}",
  AI_MODEL: "/ai-assistance",

  ASSET_TYPE: "/asset-types",
  ASSET_TYPE_CREATE: "/asset-types/create",
  ASSET_TYPE_DETAIL: "/asset-types/{0}",

  ASSET_DETAIL: "/asset/{0}",

  ROLES: "/roles",
  ROLES_CREATE: "/roles/create",
  ROLES_DETAIL: "/roles/{0}",

  PERMISSION: "/permission",
  PERMISSION_CREATE: "/permission/create",
  PERMISSION_DETAIL: "/permission/{0}",

  PROFILE: "/profile",

  MY_INSPECTIONS: "/my-inspection-missions",
  MY_INSPECTIONS_CREATE: "/my-inspection-missions/create",
  MY_INSPECTIONS_DETAIL: "/my-inspection-missions/{0}",

  INSPECTIONS_ORGANIZATION: "/inspection-missions-organization",
  INSPECTIONS_ORGANIZATION_CREATE: "/inspection-missions-organization/create",
  INSPECTIONS_ORGANIZATION_DETAIL: "/inspection-missions-organization/{0}",

  DOCS_VIEWER: `https://docs.google.com/gview?url=${window.location.origin}{0}&embedded=true`,
  PDF_VIEWER: `${window.location.origin}{0}`,
  OFFICE_APPS_VIEWER: `https://view.officeapps.live.com/op/embed.aspx?src=${window.location.origin}{0}`,
  IMAGE_VIEWER: `${window.location.origin}/api/file/preview/{0}`,

  ELEMENT: {
    CURRENT: "",
    CREATE: "create",
    DETAIL: ":id",
  },

  FOLDER_DETAIL: "/folder/{0}",
  ORGANIZATION_WORKSPACE: "/organization-workspace",
  MY_WORKSPACE: "/my-workspace",
  WORKSPACE_DETAIL: "/workspace/{0}",
  TOOLS: "/tools",
  PROJECT_DETAIL: "/project/{0}",
  CREATE_PROJECT: "/create-project",

  ACCOUNT: "/account",

  SHARE_WITH_ME: "/share-with-me",
  ERROR_404: "/error/404",
  GOOGLE_DOCS_VIEWER: `https://docs.google.com/gview?url=${window.location.origin}{0}&embedded=true`,

  ADMIN_PAGE: "/admin",
  TOOL: "/tool",
  TOOL_CREATE: "/admin/tool/create",
  TOOL_DETAIL: "/admin/tool/{0}",
  TOTAL_TOKEN_BY_USER: "/token-instruction",
  ADMIN_TOKEN_INSTRUCTION: "/admin/token-instruction",

  INSTRUCTION: "/instruction",
  ADMIN_INSTRUCTION: "/admin/instruction",
  INSTRUCTION_CREATE: "/admin/instruction/create",
  INSTRUCTION_DETAIL: "/admin/instruction/{0}",
  CREATE_DATASET: "/instruction/{0}/dataset",
  DATASET_DETAILS: "/instruction/{0}/dataset/{1}",
  STARRED: "/starred",
  OPTIONS: "/options",
  ADMIN_OPTIONS: "/admin/options",
  ADMIN_OPTIONS_CREATE: "/admin/options/create",
  ADMIN_OPTIONS_DETAIL: "/admin/options/{0}",
  OUTPUT_TYPE: "/output-type",
  ADMIN_OUTPUT_TYPE: "/admin/output-type",
  ADMIN_KNOWLEDGE_DETAIL: "/admin/knowledge/{0}",
  TEMPLATE: "/template",
  TEMPLATE_DETAILS: "/template/{0}",
  SUBSCRIPTION: "/subscription",
  TOOL_GROUP: "/group-tool",
  ORGANIZATION: "/organization",
  PAYMENT_VNPAY: "/payment/vnpay/return",
  PAYMENT_ID: "/payment/{0}",

  GOOGLE_FORM_CALLBACK: "/google-form-callback",

  PAYMENT_HISTORY: "/payment-history",
  EXCHANGE: "/exchange",
  RESOURCE: "/resource",
  MY_DASHBOARD: "/my-dashboard",
  ORG_DASHBOARD: "/organization-dashboard",
  AVERAGE_TOKEN: "/average-token",
  ADMIN_AVERAGE_TOKEN: "/admin/average-token",
  CONFIRM_REGISTER: "/confirm-register",
  CONFIRM_INVITATION: "/confirm-invitation",
  REJECT_INVITATION: "/reject-invitation",
  ACTIVATE_ACCOUNT: "/activeAccount",

  PACKAGE: "/admin/package",
  PACKAGE_ID: "/admin/package/{0}",
  PACKAGE_CREATE: "/admin/package/create",
  TOOL_STUDIO_KNOWLEDGE: "/knowledge",
  DETAIL_GROUP_TOOL: "/group-tool/{0}",
  CREATE_GROUP_TOOL: "/create-group-tool",
  ADMIN_CREATE_GROUP_TOOL: "/group-tool/create",
  ADMIN_TOOL: "/admin/tool",
  PERSONA: "/persona",
  CREATE_PERSONA: "/persona/create",
  PERSONA_DETAIL: "/persona/{0}",
  ADMIN_GROUP_TOOL: "/admin/group-tool",
  ADMIN_GROUP_TOOL_ID: "/admin/group-tool/{0}",
  ADMIN_PERSONA: "/admin/persona",
  ADMIN_CREATE_PERSONA: "/admin/persona/create",
  ADMIN_PERSONA_DETAILS: "/admin/persona/{0}",
  KNOWLEDGE_CREATE: "/admin/knowledge/create",
  ADMIN_KNOWLEDGE: "/admin/knowledge",
  CUSTOMER: "/customer",
  ADMIN_CUSTOMER: "/admin/customer",
  ADMIN_PAYMENT_HISTORY_ID: "/admin/payment-history/{0}",
  PAYMENT_HISTORY_ID: "/payment-history/{0}",
  VIDEO: "/video",
  VIDEO_ID: "/video/{0}",
  AUDIO_ID: "/audio/{0}",

  USER_TRACKING: "/user-tracking",
  GPTMODEL: "/gpt-model",

  ADMIN: {
    API_KEY: "/admin/api-key",
    DOCUMENT_TEMPLATE: "/admin/document-template",
    DOCUMENT_TEMPLATE_ID: "/admin/document-template/{0}",
    SETTING: "/admin/setting",
    ORGANIZATION: "/admin/organization",
    ORGANIZATION_ID: "/admin/organization/{0}",
    ORGANIZATION_CREATE: "/admin/organization/create",
    DISCOUNT: "/admin/discount",
    PROMOTION: "/admin/promotion",
    TRANSACTION_HISTORY: "/admin/transaction-history",
    FEEDBACK_STATISTIC: "/admin/feedback-statistics",
    FEEDBACK_ANALYSIS: "/admin/feedback-analysis",
    USER: "/admin/user",
    SUPPORT_BUSINESS: "/admin/suport-business",
    DICTATION_SHADOWING_MANAGEMENT: "/admin/dictation-shadowing-management",
    DICTATION_SHADOWING_MANAGEMENT_CREATE: "/admin/dictation-shadowing-management/create",
    DICTATION_SHADOWING_MANAGEMENT_ID: "/admin/dictation-shadowing-management/{0}",
    SPEAKING_EXERCISE: "/admin/speaking-exercise",
    SPEAKING_EXERCISE_CREATE: "/admin/speaking-exercise/create",
    SPEAKING_EXERCISE_ID: "/admin/speaking-exercise/{0}",
    ROLE_PLAY_COURSE_MANAGEMENT: "/admin/role-play-course-management",
    ROLE_PLAY_COURSE_DETAIL: "/admin/role-play-course-management/{0}",
    ROLE_PLAY_AI_PERSONA_MANAGEMENT: "/admin/role-play-ai-persona-management",
    ROLE_PLAY_AI_PERSONA_DETAIL: "/admin/role-play-ai-persona-management/{0}",
    ROLE_PLAY_AI_PERSONA_CREATE: "/admin/role-play-ai-persona-management/create",
    ROLE_PLAY_COURSE_CREATE: "/admin/role-play-course-management/create",
    ROLE_PLAY_INSTRUCTION_MANAGEMENT: "/admin/role-play-instruction-management",
    ROLE_PLAY_INSTRUCTION_DETAIL: "/admin/role-play-instruction-management/{0}",
    ROLE_PLAY_INSTRUCTION_CREATE: "/admin/role-play-instruction-management/create",
    EMAIL_CAMPAIGN: "/admin/email-campaign",
    EMAIL_CAMPAIGN_CREATE: "/admin/email-campaign/create",
    EMAIL_CAMPAIGN_ID: "/admin/email-campaign/{0}",
    EMAIL_GROUP: "/admin/email-group",
    EMAIL_GROUP_CREATE: "/admin/email-group/create",
    EMAIL_GROUP_ID: "/admin/email-group/{0}",
    EMAIL_TEMPLATE: "/admin/email-template",
    EMAIL_TEMPLATE_CREATE: "/admin/email-template/create",
    EMAIL_TEMPLATE_ID: "/admin/email-template/{0}",
    EMAIL_STATISTICS: "/admin/email-statistics",
  },

  // Explain Service
  ADMIN_EXPLAIN: "/admin/explain",
  ADMIN_EXPLAIN_CREATE: "/admin/explain/create",
  ADMIN_EXPLAIN_DETAIL: "/admin/explain/{0}",
  OPENAI_COST: "/openai-cost",
  WAITING_LIST: "/waiting-list",
  WHITELIST: "/whitelist",
  VOICE_OPTIONS: "/voice-options",
  ADMIN_VOICE_OPTIONS: "/admin/voice-options",
  ADMIN_VOICE_OPTION_CREATE: "/admin/voice-options/create",
  ADMIN_VOICE_OPTION_DETAIL: "/admin/voice-options/{0}",

  CREATE_EXAM: "/create-an-exam",
  EXAM_TEMPLATE: "/exam-template",
  EXAM_TEMPLATE_FOLDER_ID: "/exam-template/folder/{0}",

  MARK_EXAM: "/mark-an-exam",


  PRICING: "/pricing",



  COURSES: '/student/courses',
  COURSE_CREATE: '/student/courses/create',
  COURSE_DETAIL: '/student/courses/{0}',

  // Thêm links cho RolePlay
  ROLE_PLAY: '/role-play',
  ROLE_PLAY_COURSES: '/role-play/courses',
  ROLE_PLAY_COURSE_CREATE: '/role-play/courses/create',
  ROLE_PLAY_COURSE_DETAIL: '/role-play/courses/{0}',
  ROLE_PLAY_SESSION: '/role-play/session/{0}', // courseId/taskId
  ROLE_PLAY_SESSION_RESULT: '/roleplay/course/{0}/session/{1}/result', // courseId, sessionId
  ROLE_PLAY_RESULTS: '/role-play/results/{0}/{1}', // courseId/taskId
  ROLE_PLAY_AI_PERSONA: '/role-play/ai-persona',
  ROLE_PLAY_AI_PERSONA_CREATE: '/role-play/ai-persona/create',
  ROLE_PLAY_AI_PERSONA_DETAIL: '/role-play/ai-persona/{0}',
  ROLE_PLAY_TASKS: '/role-play/tasks',
  ROLE_PLAY_INSTRUCTION: '/role-play/instruction',
  ROLE_PLAY_INSTRUCTION_CREATE: '/role-play/instruction/create',
  ROLE_PLAY_INSTRUCTION_DETAIL: '/role-play/instruction/{0}',





};
