import { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useTranslation, withTranslation } from "react-i18next";
import { Form, Input } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { QuestionCircleOutlined } from "@ant-design/icons";

import AntButton from "@component/AntButton";
import { AntForm } from "@src/app/component/AntForm";
import { useOutletContext } from 'react-router-dom';

import { BUTTON, CONSTANT } from "@constant";
import RULE from "@rule";

import { login } from "@services/Auth";

import * as auth from "@src/ducks/auth.duck";
import GoogleSection from "./LoginGoogle/GoogleSection";
import LOGO from "@src/asset/logo/Logo-sacombank.svg";


function Login({ isLoading, ...props }) {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const { setAuthMessageProp, setCountSubmit, setShowRequest } = useOutletContext();

  // const [disableSubmit, setDisableSubmit] = useState(true);

  // const values = Form.useWatch([], form);

  // useEffect(() => {
  //   form
  //     .validateFields({ validateOnly: true })
  //     .then(
  //       () => {
  //         if (values?.email && values?.password) {
  //           setDisableSubmit(false);
  //         } else {
  //           setDisableSubmit(true);
  //         }
  //       },
  //       () => setDisableSubmit(true),
  //     );
  // }, [values]);

  const identifierValidator = (_, value) => {
    if (!value) {
      return Promise.resolve();
    }

    const emailPattern = new RegExp(RULE.EMAIL.type === 'email' ? /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/ : RULE.EMAIL.pattern);
    const phonePattern = new RegExp(RULE.PHONE.pattern);
    const expectedPhoneLength = RULE.PHONE.len;

    if (emailPattern.test(value)) {
      return Promise.resolve();
    }

    if (phonePattern.test(value)) {
      if (value.length === expectedPhoneLength) {
        return Promise.resolve();
      } else {
        return Promise.reject(new Error(t("INVALID_PHONE_NUMBER_LENGTH", { length: expectedPhoneLength })));
      }
    }

    return Promise.reject(new Error(t("INVALID_EMAIL_OR_PHONE_FORMAT")));
  };



  async function handleLogin(formValues) {
    const { identifier, password } = formValues;
    let apiPayload;

    // Regex để kiểm tra định dạng email đơn giản
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    // Regex để kiểm tra định dạng số điện thoại (chỉ chứa số)
    const phoneRegex = /^[0-9]+$/;

    if (emailRegex.test(identifier)) {
      apiPayload = { email: identifier, password };
    } else if (phoneRegex.test(identifier)) {
      apiPayload = { phone: identifier, password };
    } else {
      // Nếu không phải email hoặc số điện thoại hợp lệ (theo regex cơ bản)
      setAuthMessageProp({
        authTitle: t("AUTHENTICATION_FAILED"),
        authMessage: t("INVALID_EMAIL_OR_PHONE_FORMAT"), // Bạn cần thêm key này vào file i18n
        authStatus: CONSTANT.ERROR
      });
      setCountSubmit(pre => pre + 1);
      return;
    }
    const apiResponse = await login(apiPayload);
    if (apiResponse) {
      const { data, code } = apiResponse;
      if (code === 200) {
        props.userLoaded(data);
      } else if (code === 202) {
        setAuthMessageProp({
          authTitle: "AUTHENTICATION_WARING",
          authMessage: data.message,
          authStatus: CONSTANT.WARNING
        });
      } else if (code === 403) {
        setAuthMessageProp({
          authTitle: "AUTHENTICATION_FAILED",
          authMessage: apiResponse?.message || "AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR",
          authStatus: CONSTANT.ERROR
        });
        setShowRequest(true);
      } else {
        setAuthMessageProp({
          authTitle: "AUTHENTICATION_FAILED",
          authMessage: apiResponse?.message || "AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR",
          authStatus: CONSTANT.ERROR
        });
      }
    } else {
      setAuthMessageProp({
        authTitle: "AUTHENTICATION_FAILED",
        authMessage: "AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR",
        authStatus: CONSTANT.ERROR
      });
    }

    setCountSubmit(pre => pre + 1);
  }

  const handleFocus = (fieldName) => {
    form.setFields([{
      name: fieldName,
      errors: [],
    }]);
  }


  return (
    <>
      <div className="auth-content__logo-wrapper">
        <img src={LOGO} alt="" className="auth-content__logo" />
      </div>
      <div className="auth-content__title">{t("LOGIN_STB", "Đăng nhập STB Vitual Coach")}</div>
      {/* <GoogleSection
        setAuthMessageProp={setAuthMessageProp}
        setCountSubmit={setCountSubmit}
      /> */}
      <AntForm
        requiredMark={false}
        form={form}
        layout="vertical" onFinish={handleLogin}
        className="auth-content__form"
      >
        <AntForm.Item
          name="identifier"
          rules={[RULE.REQUIRED, { validator: identifierValidator }]}
          validateTrigger="onBlur"
          validateFirst
        >
          <Input size="large" placeholder={t("ENTER_EMAIL_EMAIL_STB", "Nhập email")} onFocus={() => handleFocus('identifier')} />
        </AntForm.Item>

        <AntForm.Item name="password" rules={[RULE.REQUIRED]} validateTrigger="onBlur">
          <Input.Password
            size="large"
            placeholder={t("PASSWORD_STB", "Nhập mật khẩu")}
            onFocus={() => handleFocus('password')}
            onPressEnter={form.submit} />
        </AntForm.Item>
      </AntForm>

      <Link to="forgot-password" className="auth-content__redirect">{t("FORGOT_PASSWORD_STB", "Quên mật khẩu")}</Link>

      <AntButton
        size="large"
        className="auth-content__submit"
        block
        // type={BUTTON.DEEP_NAVY}
        onClick={form.submit}
      // disabled={disableSubmit}
      >
        {t("LOGIN_STB", "Đăng nhập")}
      </AntButton>

      {/* <div className="auth-content__question">
        <span>{t('DONT_HAVE_AN_ACCOUNT')}</span>
        <Link to="sign-up" className="auth-content__question__redirect">{t('SIGN_UP_HERE')}</Link>
      </div> */}
    </>
  );
}

function mapStateToProps(store) {
  return {};
}

export default withTranslation()(connect(mapStateToProps, auth.actions)(Login));

