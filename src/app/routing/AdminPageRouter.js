import { Navigate, Route, Routes } from "react-router-dom";
import { connect } from "react-redux";

import { LINK } from "@link";
import { CONSTANT, USER_TYPE } from "@constant";
import { hasAdminAccess, hasRouteAccess } from "@src/utils/adminPermissions";

import NeedAccess from "../component/NeedAccess";
import AdminPermissionGuard from "@src/components/auth/AdminPermissionGuard";

import Settings from "@src/app/pages/AdminPage/Settings";

// Import màn hình quản lý khoá học RolePlay
import CoursesManagementScreen from "@app/pages/RolePlay/Admin/CoursesManagementScreen";
// Import màn hình quản lý AI Persona
import AIPersonaManagementScreen from "@app/pages/RolePlay/Admin/AIPersonaManagementScreen";
// Import màn hình chi tiết AI Persona
import AIPersonaDetailScreen from "@app/pages/RolePlay/Admin/AIPersonaDetailScreen";
// Import màn hình chi tiết Course
import CourseDetailScreen from "@app/pages/RolePlay/Admin/CourseDetailScreen";
// Import màn hình quản lý RolePlayInstruction
import RolePlayInstructionManagementScreen from "@app/pages/RolePlay/Admin/RolePlayInstructionManagementScreen";
// Import màn hình chi tiết RolePlayInstruction
import RolePlayInstructionDetailScreen from "@app/pages/RolePlay/Admin/RolePlayInstructionDetailScreen";



const AdminPageRouter = ({ user }) => {

  function linkToAdmin(adminUrl) {
    return adminUrl.replace(LINK.ADMIN_PAGE, "");
  }

  // Kiểm tra quyền truy cập admin interface tổng quát
  if (!hasAdminAccess(user)) {
    return <NeedAccess />;
  }

  return (
    <Routes>
      <Route>
        {/* Settings Route - Chỉ System Admin và Teacher */}
        <Route
          path={linkToAdmin(LINK.ADMIN.SETTING)}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.SETTING}>
              <Settings />
            </AdminPermissionGuard>
          }
        />

        {/* RolePlay Course Management - System Admin, Teacher và Regular Admin */}
        <Route
          path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT)}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT}>
              <CoursesManagementScreen />
            </AdminPermissionGuard>
          }
        />

        {/* RolePlay AI Persona Management - System Admin, Teacher và Regular Admin */}
        <Route
          path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT)}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT}>
              <AIPersonaManagementScreen />
            </AdminPermissionGuard>
          }
        />

        {/* AI Persona Detail Routes */}
        <Route
          path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_CREATE)}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT}>
              <AIPersonaDetailScreen />
            </AdminPermissionGuard>
          }
        />
        <Route
          path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_DETAIL.format(":id"))}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT}>
              <AIPersonaDetailScreen />
            </AdminPermissionGuard>
          }
        />

        {/* Course Detail Routes */}
        <Route
          path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_COURSE_CREATE)}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT}>
              <CourseDetailScreen />
            </AdminPermissionGuard>
          }
        />
        <Route
          path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_COURSE_DETAIL.format(":id"))}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT}>
              <CourseDetailScreen />
            </AdminPermissionGuard>
          }
        />

        {/* RolePlayInstruction Routes - Chỉ System Admin và Teacher */}
        <Route
          path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT)}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT}>
              <RolePlayInstructionManagementScreen />
            </AdminPermissionGuard>
          }
        />
        <Route
          path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_INSTRUCTION_CREATE)}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT}>
              <RolePlayInstructionDetailScreen />
            </AdminPermissionGuard>
          }
        />
        <Route
          path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_INSTRUCTION_DETAIL.format(":id"))}
          element={
            <AdminPermissionGuard requiredRoute={LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT}>
              <RolePlayInstructionDetailScreen />
            </AdminPermissionGuard>
          }
        />

        <Route path="*" element={<Navigate to={LINK.ERROR_404} replace />} />
      </Route>
    </Routes>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(AdminPageRouter);
