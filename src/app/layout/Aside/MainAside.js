import { connect } from "react-redux";
import { useTranslation } from "react-i18next";

import AsideItem from "@app/layout/Aside/AsideItem";
import { hasRouteAccess, getAllowedRoutes } from "@src/utils/adminPermissions";

import { LINK } from "@link";
import { USER_TYPE } from "@constant";

// Admin icons
import SETTINGS_ICON from "@src/asset/aside/token-instruction.svg";
import SETTINGS_ICON_ACTIVE from "@src/asset/aside/token-instruction-active.svg";
import COURSE_ICON from "@src/asset/aside/token-instruction.svg";
import COURSE_ICON_ACTIVE from "@src/asset/aside/token-instruction-active.svg";
import AI_PERSONA_ICON from "@src/asset/aside/persona.svg";
import AI_PERSONA_ICON_ACTIVE from "@src/asset/aside/persona-active.svg";

function MainAside({ user }) {
  const { t } = useTranslation();

  // Render a menu item using the same style as regular pages
  const renderMenuItem = (item) => {
    return (
      <AsideItem
        key={item.linkTo}
        linkTo={item.linkTo}
        title={t(item.title)}
        img={item.img}
        imgActive={item.imgActive}
      />
    );
  };

  // Define menu items based on user permissions
  const getMenuItems = () => {
    // Tất cả các menu items có thể có (Settings được đặt cuối cùng)
    const allMenuItems = [
      {
        linkTo: LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT,
        title: "COURSE_MANAGEMENT",
        img: COURSE_ICON,
        imgActive: COURSE_ICON_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT,
        title: "AI_PERSONA_MANAGEMENT",
        img: AI_PERSONA_ICON,
        imgActive: AI_PERSONA_ICON_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT,
        title: "INSTRUCTION_MANAGEMENT",
        img: SETTINGS_ICON,
        imgActive: SETTINGS_ICON_ACTIVE
      },
      {
        linkTo: LINK.ADMIN.SETTING,
        title: "SETTINGS",
        img: SETTINGS_ICON,
        imgActive: SETTINGS_ICON_ACTIVE
      }
    ];

    // Lọc menu items dựa trên quyền của user
    return allMenuItems.filter(item => hasRouteAccess(user, item.linkTo));
  };

  return (
    <div className="aside-body">
      {getMenuItems().map(item => renderMenuItem(item))}
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(MainAside);
