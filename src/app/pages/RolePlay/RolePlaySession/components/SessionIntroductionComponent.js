import React, { useEffect, useState } from 'react';
import { Card, List, Avatar, Button, message, Tooltip } from 'antd';
import { ArrowLeftOutlined, FileTextOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { API } from '@src/constants/api';
import AntButton from '@component/AntButton';
import { BUTTON } from '@constant';
import ReactMarkdown from 'react-markdown';
import { Modal } from 'antd';
import { RolePersona } from '@src/app/component/SvgIcons/IconPersona/rolePersona';
import { Organization } from '@src/app/component/SvgIcons/IconPersona/organization';
import { Mood } from '@src/app/component/SvgIcons/IconPersona/mood';
import avatar from "@src/asset/image/avatar-persona.png";
import { getAIPersonaDetails, getAIScenariosDetails } from '@src/app/services/RolePlay';
import IDEA_SHOW from "@src/asset/icon/Idea-show.svg";
import IDEA_CLOSE from "@src/asset/icon/Idea-close.svg";
import Paper from "@src/asset/icon/Paper-1.svg";
import clsx from 'clsx';

const SessionIntroductionComponent = ({
  course,
  tasksList,
  persona,
  onStartSession,
  onBackToCourseList,
  isStartButtonDisabled,
  socketStatus,
  SOCKET_STATUS,
  scenarios,
  setPersona,
  setScenariosDetail,
}) => {
  const { t } = useTranslation();
  const totalEstimatedDuration = course?.estimatedCallTimeInMinutes || 'N/A';

  // Thêm state để quản lý modal hiển thị nội dung reference
  const [selectedReference, setSelectedReference] = useState(null);

  const [select, setSelect] = useState(1);

  const handleOnStartSession = async (aiPersonaId, aiscenarios) => {
    const success = await handleGetPersona(aiPersonaId);
    const success2 = await handleGetScenariosDetail(aiscenarios);
    if (success && success2) {
      onStartSession();
    }
  };

  const handleGetPersona = async (aiPersonaId) => {
    try {
      const personaDetails = await getAIPersonaDetails(aiPersonaId);
      if (personaDetails) {
        setPersona(personaDetails);
        return true;
      } else {
        message.warning(t('NO_PERSONA_ASSIGNED', 'Khóa học này chưa được gán AI Persona.'));
        return false;
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      return false;
    }
  };

  const handleGetScenariosDetail = async (aiscenariosId) => {
    try {
      const scenariosDetails = await getAIScenariosDetails(aiscenariosId);
      if (scenariosDetails) {
        setScenariosDetail(scenariosDetails);
        return true;
      } else {
        message.warning(t('NO_PERSONA_ASSIGNED', 'Khóa học này chưa được gán kịch bản.'));
        return false;
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      return false;
    }
  }


  return (
    <Card className="role-play-session-screen__introduction">
      <div className="role-play-session-screen__header">
        <AntButton
          icon={<ArrowLeftOutlined />}
          onClick={onBackToCourseList}
          type={BUTTON.TEXT}
          className="role-play-session-screen__back-button"
        >
          {t('BACK_TO_COURSE_LIST', 'Quay lại danh sách khóa học')}
        </AntButton>
      </div>
      <h1 className="role-play-session-screen__title">{course?.name}</h1>
      <div className='time-progress-wrapper'>
        <p className="course-time">{totalEstimatedDuration} phút</p>
        <p className="course-progress">{course.progress || "N/A"}% hoàn thành</p>
      </div>
      <div className='flex gap-4 mb-10'>
        <button className={`select-btn ${select === 1 ? 'active' : ''}`} onClick={() => setSelect(1)}>
          {t('COURSE_INFO', 'Thông tin khóa học')}
        </button>
        <button className={`select-btn ${select === 2 ? 'active' : ''}`} onClick={() => setSelect(2)}>
          {t('LAB_SESSION', 'Thực hành với AI Coach')}
        </button>
      </div>

      {select === 1 ? <>
        {course?.description && (
          <div className="role-play-session-screen__course-description" style={{ marginBottom: '40px' }}>
            <h2 className="role-play-session-screen__description-title" style={{ fontSize: '18px', fontWeight: '600', marginBottom: '8px', color: "#0c4da2" }}>
              {t('COURSE_DESCRIPTION', 'Mô tả khóa học')}
            </h2>
            <p className='m-0 text-base leading-6 font-normal whitespace-pre-line'>{course.description}</p>
          </div>
        )}

        {/* {tasksList && tasksList.length > 0 && (
          <div className="role-play-session-screen__tasks-list" style={{ marginBottom: '20px' }}>
            <h2 className="role-play-session-screen__tasks-title" style={{ fontSize: '18px', fontWeight: '600', marginBottom: '10px' }}>
              {t('TASKS_IN_COURSE', 'Các nhiệm vụ trong khóa học')}
            </h2>
            <List
              bordered
              dataSource={tasksList}
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    title={item.name}
                    description={item.description}
                  />
                </List.Item>
              )}
            />
          </div>
        )} */}

        {course?.references && course.references.length > 0 && (
          <div className="flex">
            <div className='w-1' style={{ backgroundColor: "#0c4da2" }}></div>
            <div className='role-play-session-screen__reference-materials'>
              <h2 className="role-play-session-screen__reference-title">
                {t('REFERENCE_MATERIALS', 'Tài liệu tham khảo')}
              </h2>
              <div className="reference-list">
                {course.references.filter(ref => ref.isPublic).map(reference => {
                  const isSelected = selectedReference?._id === reference._id && !!selectedReference?.content;

                  return (
                    <div key={reference._id} className="reference-item">
                      <div className='h-[0.5px] bg-gray-300 w-full my-4'></div>
                      <div className='flex items-start gap-2'>
                        <Tooltip
                          title="Tóm tắt nội dung chính"
                          className='cursor-pointer'
                          onClick={() => {
                            setSelectedReference(reference);
                            // setShowReferenceContent(true);
                          }}
                        >
                          <img src={isSelected ? IDEA_SHOW : IDEA_CLOSE} alt='' />
                        </Tooltip>
                        <a href={reference.url} target="_blank" rel="noopener noreferrer" className='max-w-[676px] mt-[2px] break-all'>
                          {reference.name || reference.url}
                        </a>
                      </div>
                      {isSelected && (
                        <div className='mt-2 pl-8'>
                          <div className='reference-content'>
                            <div className='flex items-center justify-between'>
                              <div className='flex items-center gap-1'>
                                <img src={Paper} alt='' />
                                <span className='reference-content__title'>{t('CONTENT_MAIN', 'Tóm tắt nội dung chính')}</span>
                              </div>
                              <button className='reference-content__close' onClick={() => setSelectedReference(null)}>
                                Đóng
                              </button>
                            </div>
                            <div className='ml-[3px]'>
                              <ReactMarkdown>
                                {selectedReference.content}
                              </ReactMarkdown>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}

              </div>
            </div>
          </div>
        )}

      </> : <>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {scenarios?.map((persona, index) => (
            <div
              key={index}
              className="persona-card flex flex-col"
            >
              <div className="flex items-center gap-2">
                {persona.aiPersonaId.avatarId && (
                  <div className='persona-card__avatar-wrapper'>
                    <img
                      src={API.STREAM_ID.format(persona.aiPersonaId.avatarId)}
                      alt=''
                      className="persona-card__avatar"
                    />
                  </div>
                )}
                <h3 className="persona-card__name m-0">{persona.aiPersonaId.name}</h3>
              </div>

              <p className="persona-card__background ">{persona.aiPersonaId.personaBackground}</p>

              <div className='flex items-center gap-2 mt-auto'>
                <div className='persona-card__timer'>
                  {persona.estimatedCallTimeInMinutes} phút
                </div>
                <div className={clsx("persona-card__completed", { "persona-card__unfinished": !persona.isCompleted })}>
                  {persona.isCompleted ? 'Đã hoàn thành' : 'Chưa hoàn thành'}
                </div>
              </div>

              <div className="persona-card__concern">
                <div className='flex gap-1 items-center h-[21px]'>
                  <div className='flex items-center'>
                    <RolePersona />
                  </div>
                  <p className='m-0 '><strong>Vai trò:</strong> {persona.aiPersonaId.role}</p>
                </div>
                <div className='flex gap-1 items-center h-[21px]'>
                  <div className='flex items-center'>
                    <Organization />
                  </div>
                  <p className='m-0 '><strong>Tổ chức:</strong> {persona.aiPersonaId.organization}</p>
                </div>
                <div className='flex gap-1 items-center h-[21px]'>
                  <div className='flex items-center'>
                    <Mood />
                  </div>
                  <strong>Tâm trạng:</strong>{' '}
                  <span className={persona.aiPersonaId.moodColor}>{persona.aiPersonaId.mood}</span>
                </div>
              </div>

              <button className="persona-card__start-button"
                onClick={() => handleOnStartSession(persona.aiPersonaId._id, persona._id)}
                disabled={isStartButtonDisabled}
              >
                {socketStatus === SOCKET_STATUS.CONNECTING ? t('CONNECTING', 'Đang kết nối...') : t('START_SESSION', 'Bắt đầu')}
              </button>
            </div>
          ))}
        </div>

      </>}



    </Card>
  );
};

export default SessionIntroductionComponent;
