.roleplay-instruction-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .roleplay-instruction-info-card,
  .roleplay-instruction-search-card,
  .roleplay-instruction-table-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .roleplay-instruction-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .roleplay-instruction-title {
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      color: var(--typo-colours-primary-black);
      margin-bottom: 8px;
    }

    .roleplay-instruction-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey) --typo-colours-secondary-grey is not defined;
      margin-bottom: 0;
    }
  }

  .form-filter {
    .search-form-item {
      margin-bottom: 0;
    }

    .search-buttons-col {
      display: flex;
      justify-content: flex-end;

      .search-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }

  .roleplay-instruction-actions {
    display: flex;
    justify-content: center;
    gap: 8px;

    .btn-edit,
    .btn-delete {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .roleplay-instruction-table {
    .name-value {
      font-weight: 500;
    }

    .roleplay-instruction-table-row {
      &:hover {
        cursor: pointer;
      }
    }
  }
}