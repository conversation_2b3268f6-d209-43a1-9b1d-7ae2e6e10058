.courses-management-container {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .courses-management-info-card,
  .courses-management-search-card,
  .courses-management-table-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .courses-management-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .courses-management-title {
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      color: var(--typo-colours-primary-black);
      margin-bottom: 8px;
    }

    .courses-management-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey) --typo-colours-secondary-grey is not defined;
      margin-bottom: 0;
    }

    .btn-create-course {
      margin-left: auto;
    }
  }

  .form-filter {
    .search-form-item {
      margin-bottom: 0;
    }

    .search-buttons-col {
      .search-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-end;

        .ant-btn {
          min-width: 100px;
        }
      }
    }
  }

  .courses-management-table-card {
    .ant-table {
      .ant-table-container {
        .ant-table-thead>tr>th {
          background: #fafafa;
        }
      }
    }

    .course-actions {
      display: flex;
      gap: 8px;
      justify-content: center;

      .btn-edit-course,
      .btn-delete-course {
        border: none;
        background: none;
        color: #1890ff;
        transition: color 0.3s;

        &:hover {
          color: #40a9ff;
        }
      }

      .btn-delete-course {
        color: #ff4d4f;

        &:hover {
          color: #ff7875;
        }
      }
    }

    .course-title-value {
      color: #1890ff;
      transition: color 0.3s;

      &:hover {
        color: #40a9ff;
      }
    }
  }
}