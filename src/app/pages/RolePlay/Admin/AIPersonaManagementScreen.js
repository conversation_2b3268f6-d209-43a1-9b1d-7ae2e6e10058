import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import i18n from "i18next";
import { Link, useLocation } from "react-router-dom";
import { Card, Form, Input, Row, Col, Select, Tooltip } from "antd"; // Bỏ Tag nếu không dùng
import { SearchOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";
import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";

import { LINK } from "@link";
import { BUTTON, PAGINATION_INIT } from "@constant"; // Bỏ SIMULATION_TYPE_OPTIONS nếu không dùng

import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, navigateAfterDelete, orderColumn, paginationConfig, handleReplaceUrlSearch } from "@common/functionCommons";

import { getAllAIPersonas, deleteAIPersona } from "@services/RolePlay/AIPersonaService";

import "./AIPersonaManagementScreen.scss"; // Import SCSS nếu cần

const AIPersonaManagementScreen = ({ ...props }) => {
  const { t } = useTranslation();
  const location = useLocation();

  const [personasData, setPersonasData] = useState(PAGINATION_INIT);
  const [formFilter] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    fetchPersonasData(paging, query);
  }, [location.search]);

  async function fetchPersonasData(
    paging = personasData.paging,
    query = personasData.query,
  ) {
    setIsLoading(true);
    // AIPersona không cần populate theo sample data hiện tại, nhưng nếu API thay đổi thì cần thêm
    const apiResponse = await getAllAIPersonas(query, paging, []);
    if (apiResponse) {
      setPersonasData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  }

  async function handleDeletePersona(personaId, personaName) {
    confirm.delete({
      title: t("DELETE_AI_PERSONA"),
      content: t("DELETE_AI_PERSONA_CONFIRM", { title: personaName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setIsLoading(true);
        const apiResponse = await deleteAIPersona(personaId);
        setIsLoading(false);
        if (apiResponse) {
          toast.success(t("DELETE_AI_PERSONA_SUCCESS"));
          navigateAfterDelete(personasData, fetchPersonasData);
        } else {
          toast.error(t("DELETE_AI_PERSONA_ERROR"));
        }
      },
    });
  }

  // const handleCreatePersona = async (values) => { ... } // Sẽ thêm sau

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, personasData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, personasData.paging.pageSize, {});
  };

  const columns = [
    orderColumn(personasData.paging),
    {
      title: t("AI_PERSONA_NAME"),
      dataIndex: "name",
      width: 200,
      render: (text, record) => (
        // Nên có link đến trang chi tiết Persona nếu có
        <Link to={LINK.ADMIN.ROLE_PLAY_AI_PERSONA_DETAIL.format(record._id)} className="persona-title-value">
          {text}
        </Link>
      ),
    },
    {
      title: t("AGE"),
      dataIndex: "age",
      width: 100,
    },
    {
      title: t("GENDER"),
      dataIndex: "gender",
      width: 100,
    },
    {
      title: t("AI_PERSONA_ROLE"),
      dataIndex: "role",
      width: 150,
    },
    {
      title: t("AI_PERSONA_MOOD"),
      dataIndex: "mood",
      width: 120,
    },
    {
      title: t("AI_PERSONA_ORGANIZATION"), // Hiển thị tên organization
      dataIndex: "organization", // Giữ nguyên vì sample data trả về string, nếu là objectId cần populate và đổi thành ["organizationId", "name"]
      width: 200,
    },
    {
      title: t("AI_PERSONA_BACKGROUND"),
      dataIndex: "personaBackground",
      width: 300,
      ellipsis: true,
    },
    {
      title: t("ACTION"),
      width: 120,
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <div className="persona-actions">
          <Tooltip title={t("EDIT_AI_PERSONA")}>
            <Link to={LINK.ADMIN.ROLE_PLAY_AI_PERSONA_DETAIL.format(record._id)}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className="btn-edit-persona"
                icon={<EditOutlined />}
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_AI_PERSONA")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className="btn-delete-persona"
              icon={<DeleteIcon />}
              onClick={() => handleDeletePersona(record._id, record.name)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <Loading active={isLoading} transparent>
      <div className="ai-persona-management-container">
        <Card className="ai-persona-management-info-card">
          <div className="ai-persona-management-info-header">
            <div>
              <h1 className="ai-persona-management-title">{t("ROLE_PLAY_AI_PERSONA_MANAGEMENT")}</h1>
              <p className="ai-persona-management-description">{t("ROLE_PLAY_AI_PERSONA_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN.ROLE_PLAY_AI_PERSONA_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create-persona"
                icon={<PlusOutlined />}
              >
                {t("CREATE_AI_PERSONA")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="ai-persona-management-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={12} lg={10}>
                <Row gutter={16}>
                  <Col xs={24} md={12}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_AI_PERSONA_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12}>
                    <AntForm.Item name="role" className="search-form-item">
                      {/* Nếu có danh sách role cố định thì dùng Select, nếu không thì dùng Input */}
                      <Input
                        placeholder={t("FILTER_BY_AI_PERSONA_ROLE")}
                        allowClear
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={12} lg={4} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="ai-persona-management-table-card">
          <TableAdmin
            scroll={{ x: 1200 }} // Điều chỉnh scroll nếu cần
            columns={columns}
            dataSource={personasData.rows} // API trả về rows trong data
            loading={isLoading}
            pagination={paginationConfig(personasData.paging, personasData.query, i18n.language)}
            rowKey="_id"
          />
        </Card>
      </div>
      {/* Modal tạo AI Persona sẽ được thêm sau */}
    </Loading>
  );
};

export default AIPersonaManagementScreen;
